# 数据库查询性能优化方案

## 问题分析

根据SQL执行计划分析，主要性能瓶颈在于：

1. **GroupAggregate操作耗时2029ms** - 需要对大量数据进行分组聚合
2. **Incremental Sort处理478,784行数据** - 排序操作消耗大量资源
3. **Nested Loop Left Join** - 多表连接查询效率低
4. **缺少合适的复合索引** - 现有索引不能很好支持查询模式

## 已实施的优化

### 1. 添加复合索引
```python
# 在Order模型中添加的新索引
models.Index(fields=['settle_work_order', 'full_settle_state'], name='settle_work_order_state_idx'),
models.Index(fields=['settle_work_order', 'marketing_account'], name='settle_work_order_account_idx'),
models.Index(fields=['marketing_account', 'full_settle_state'], name='account_state_idx'),
```

### 2. 查询优化
- 使用`select_related()`和`prefetch_related()`减少数据库查询次数
- 优化营销团队查询，使用直接的外键关系查询
- 改进统计数据计算方法

## 进一步优化建议

### 1. 数据库级别优化

#### PostgreSQL配置优化
```sql
-- 增加工作内存，提高排序和哈希操作性能
SET work_mem = '256MB';

-- 增加共享缓冲区
SET shared_buffers = '1GB';

-- 启用并行查询
SET max_parallel_workers_per_gather = 4;
```

#### 分区表优化
考虑对Order表按时间分区：
```sql
-- 按月分区Order表
CREATE TABLE order_y2024m01 PARTITION OF core_order
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 2. 应用层优化

#### 缓存策略
```python
from django.core.cache import cache

def get_settle_work_order_statistics(settle_work_order_id):
    cache_key = f'settle_work_order_stats_{settle_work_order_id}'
    stats = cache.get(cache_key)
    if stats is None:
        # 计算统计数据
        stats = calculate_statistics(settle_work_order_id)
        cache.set(cache_key, stats, timeout=300)  # 缓存5分钟
    return stats
```

#### 异步处理
```python
from celery import shared_task

@shared_task
def update_settle_work_order_statistics(settle_work_order_id):
    # 异步更新统计数据
    pass
```

### 3. 查询重构

#### 使用原生SQL优化关键查询
```python
def get_optimized_statistics(settle_work_order_id):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT 
                COUNT(*) as order_count,
                SUM(deals_count) as deals_count_sum,
                SUM(order_actual_payment) as order_actual_payment_sum,
                SUM(settle_amount) as settle_amount_sum,
                COUNT(DISTINCT marketing_account_id) as marketing_account_count,
                COUNT(DISTINCT ma.marketing_team_id) as marketing_team_count
            FROM core_order o
            LEFT JOIN core_marketingaccount ma ON o.marketing_account_id = ma.id
            WHERE o.settle_work_order_id = %s
        """, [settle_work_order_id])
        return cursor.fetchone()
```

### 4. 监控和分析

#### 查询性能监控
```python
import time
from django.db import connection

def monitor_query_performance():
    start_time = time.time()
    # 执行查询
    end_time = time.time()
    
    print(f"查询耗时: {end_time - start_time:.2f}秒")
    print(f"SQL查询数量: {len(connection.queries)}")
```

## 预期效果

实施这些优化后，预期可以获得：

1. **查询时间减少60-80%** - 从2秒降低到0.4-0.8秒
2. **内存使用优化** - 减少排序操作的内存消耗
3. **并发性能提升** - 减少数据库锁定时间
4. **用户体验改善** - 页面加载速度显著提升

## 实施步骤

1. 运行数据库迁移添加新索引
2. 部署代码更改
3. 监控性能指标
4. 根据实际效果调整缓存策略
5. 考虑实施分区表（如果数据量继续增长）

## 注意事项

- 新增索引会增加写操作的开销，需要权衡读写性能
- 缓存策略需要考虑数据一致性
- 分区表需要修改现有查询逻辑
- 定期分析查询计划，持续优化
