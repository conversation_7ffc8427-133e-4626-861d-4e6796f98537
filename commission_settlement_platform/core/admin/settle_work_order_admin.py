from django.contrib import admin, messages
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator, EmptyPage
from django.db import transaction
from django.db.models import Sum
from django.forms import Form
from django.shortcuts import render, redirect
from django.urls import path, reverse
from django.utils.safestring import SafeString

from commission_settlement_platform.core.admin.display_functions import (
    settling_amount,
    settled_amount,
    can_not_settle_amount,
    abnormal_settle_amount,
    approved_settle_amount,
    abnormal_settle_amount_rate,
    task_processing,
)
from commission_settlement_platform.core.admin.export_statistics_csv_tools import (
    SettleWorkOrderOldSystemFormatCSVExporter, SettleWorkOrderCSVExporter
)
from commission_settlement_platform.core.admin.querysets import get_settle_work_order_data
from commission_settlement_platform.core.models import SettleWorkOrder, concrete_fields, MarketingTeam, Order
from commission_settlement_platform.core.utils import compact_all_falsy_dict


@admin.register(SettleWorkOrder)
class SettleWorkOrderAdmin(admin.ModelAdmin):
    @admin.display(
        description='订单统计',
        ordering='order_created_date'
    )
    def order_statistics(self, obj):
        # 写在 Admin 中，是因为需要传入 request
        view = self.order_statistics_view(self.request, obj.id)
        return SafeString(view.content.decode('utf-8'))

    @admin.display(
        description='订单应付金额求和',
        ordering='order_actual_payment_sum'
    )
    def order_actual_payment_sum(self, obj):
        return obj.order_actual_payment_sum

    @admin.display(
        description='销售数量求和',
        ordering='deals_count_sum'
    )
    def deals_count_sum(self, obj):
        return obj.deals_count_sum

    @admin.display(
        description='结算金额求和',
        ordering='settle_amount_sum'
    )
    def settle_amount_sum(self, obj):
        return obj.settle_amount_sum

    @admin.display(
        description='订单数量',
        ordering='order_count'
    )
    def order_count(self, obj):
        return obj.order_count

    @admin.display(
        description='营销团队数量',
        ordering='marketing_team_count'
    )
    def marketing_team_count(self, obj):
        return obj.marketing_team_count

    @admin.display(
        description='营销账号数量',
        ordering='marketing_account_count'
    )
    def marketing_account_count(self, obj):
        return obj.marketing_account_count

    def get_form(self, request, obj=None, **kwargs):
        # 这里需要传入 request，否则在 order_statistics_view 中无法获取 request
        self.request = request
        return super().get_form(request, obj, **kwargs)

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('<path:object_id>/order-statistics/',
                 self.admin_site.admin_view(self.order_statistics_view),
                 name='settle_work_order_order_statistics'),
            path('<path:object_id>/export-csv/',
                 self.admin_site.admin_view(self.export_order_statistics_csv),
                 name='settle_work_order_export_csv'),
            path('<path:object_id>/export-old-system-format-csv/',
                 self.admin_site.admin_view(self.export_old_system_format_order_statistics_csv),
                 name='settle_work_order_export_old_system_format_csv'),
        ]
        return custom_urls + urls

    def export_order_statistics_csv(self, request, object_id):
        """导出订单摘要为CSV文件"""
        if object_id:
            # 从请求中获取结算状态过滤条件（如果有）
            marketing_team_id = request.GET.get('marketing_team_id', None)
            csv_exporter = SettleWorkOrderCSVExporter(
                settle_work_order_id=object_id,
                marketing_account__marketing_team_id=marketing_team_id,
            )
            return csv_exporter.get_csv_response()
        return None

    def export_old_system_format_order_statistics_csv(self, request, object_id):
        """导出订单摘要为CSV文件"""
        if object_id:
            # 从请求中获取结算状态过滤条件（如果有）
            marketing_team_id = request.GET.get('marketing_team_id', None)
            csv_exporter = SettleWorkOrderOldSystemFormatCSVExporter(
                settle_work_order_id=object_id,
                marketing_account__marketing_team_id=marketing_team_id,
            )
            return csv_exporter.get_csv_response()
        return None

    def order_statistics_view(self, request, object_id):
        if object_id:
            # 从请求中获取结算状态过滤条件
            marketing_team_id = request.GET.get('marketing_team_id', None)

            # 获取订单聚合数据
            order_aggregated_data = get_settle_work_order_data(object_id, marketing_team_id)

            # 计算统计数据 - 使用select_related优化查询
            orders = Order.objects.select_related('marketing_account__marketing_team').filter(
                settle_work_order_id=object_id
            )
            statistics = orders.filter(
                **compact_all_falsy_dict({
                    'marketing_account__marketing_team_id': marketing_team_id
                })
            ).aggregate(
                order_actual_payment_sum=Sum('order_actual_payment'),
                settle_amount_sum=Sum('settle_amount'),
                deals_count_sum=Sum('deals_count')
            )

            # 获取相关的营销团队列表 - 使用exists()子查询优化
            marketing_team_list = MarketingTeam.objects.filter(
                marketingaccount__order__settle_work_order_id=object_id
            ).distinct()

            # 每页显示10条数据
            paginator = Paginator(order_aggregated_data, 10)
            page = request.GET.get('page', 1)

            try:
                page_obj = paginator.page(page)
            except EmptyPage:
                page_obj = paginator.page(1)

            # 获取分页范围，只显示当前页相邻的2个页码
            page_range = paginator.get_elided_page_range(number=page_obj.number, on_each_side=2, on_ends=0)

            context = {
                'page_obj': page_obj,
                'paginator': paginator,
                'page_range': page_range,  # 使用自定义的分页范围
                'object_id': object_id,
                'marketing_team_list': marketing_team_list,
                'marketing_team_id': marketing_team_id,  # 确保将marketing_team_id传递给模板
                'statistics': statistics,  # 添加总计值到context
                'export_csv_url': reverse('admin:settle_work_order_export_csv', args=[object_id]),
            }

            if request.headers.get('HX-Request'):
                return render(
                    request,
                    'admin/settleworkorder/order_statistics_table_content.html',
                    context
                )
            else:
                return render(
                    request,
                    'admin/settleworkorder/order_statistics_table.html',
                    context
                )
        return '-'

    def get_actions(self, request):
        actions = super().get_actions(request)
        if "delete_selected" in actions:
            del actions["delete_selected"]
        return actions

    def has_add_permission(self, request):
        return False

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        if request.resolver_match.view_name in [
            'admin:core_settleworkorder_changelist', 'admin:core_settleworkorder_change'
        ]:
            queryset = SettleWorkOrder.contribute_order_statistics(super().get_queryset(request))
        return queryset

    def delete_view(self, request, object_id, extra_context=None):
        # 在 models 里虽然写了限制，但是传入 models 后性能太差
        # 所以在 view 中直接判断，提前抛出异常
        try:
            obj = SettleWorkOrder.objects.get(pk=object_id)
            if obj.has_associated_orders:
                raise ValidationError('无法删除已关联订单的结算工单')
            if obj.processing:
                raise ValidationError('无法删除正在执行任务的结算工单')
            if obj.order_set.filter(full_settle_state='已结算').exists():
                raise ValidationError('无法删除结算工单，因为它包含"🟢已结算"的订单')
            return super().delete_view(request, object_id, extra_context=extra_context)
        except ValidationError as e:
            self.message_user(request, e.message, level=messages.ERROR)
            return redirect(reverse('admin:core_settleworkorder_change', args=[object_id]))

    def save_model(self, request, obj: SettleWorkOrder, form: Form, change):
        with transaction.atomic():
            if obj.processing:
                self.message_user(request, '正在执行任务，禁止保存', level=messages.ERROR)
                return redirect(request.path)
            from celery import chain
            from commission_settlement_platform.core.tasks import (
                associate_orders_to_settle_work_order,
                disassociate_orders_from_settle_work_order,
                update_orders_when_approved
            )

            # 构建任务链
            tasks = []

            associate_orders_task = (
                associate_orders_to_settle_work_order if obj.has_associated_orders
                else disassociate_orders_from_settle_work_order
            )
            has_associated_orders = form.cleaned_data['has_associated_orders']
            approved = form.cleaned_data['approved']
            tasks.append(associate_orders_task.si(obj.pk, has_associated_orders))
            tasks.append(update_orders_when_approved.si(obj.pk, approved))

            if not approved and not has_associated_orders:
                tasks = reversed(tasks)

            try:
                # 执行任务链
                task_chain = chain(*tasks)
                return obj.save(tasks=task_chain)
            except ValidationError as e:
                self.message_user(request, str(e), level=messages.ERROR)
                return redirect(request.path)

    # inlines = [OrderInline]
    statistics_field = [
        'order_count',
        'deals_count_sum',
        'order_actual_payment_sum',
        'settle_amount_sum',
        'marketing_team_count',
        'marketing_account_count',
        settling_amount,
        settled_amount,
        can_not_settle_amount,
        abnormal_settle_amount,
        approved_settle_amount,
        abnormal_settle_amount_rate,
    ]
    list_display = concrete_fields(
        SettleWorkOrder,
        [
            'task_id',
            'selected_marketing_account_id_list',
            'selected_marketing_team_id_list'
        ]
    ) + [task_processing] + statistics_field
    fieldsets = [
        (None, {
            'fields': concrete_fields(
                SettleWorkOrder,
                [
                    'selected_marketing_account_id_list',
                    'selected_marketing_team_id_list'
                ]
            ) + [task_processing] + statistics_field
        }),
        ('订单统计', {
            'fields': ['order_statistics'],
        })
    ]
    readonly_fields = concrete_fields(
        SettleWorkOrder,
        [
            # 'task_id',
            'approved',
            'has_associated_orders',
            'selected_order_start_dt',
            'selected_order_end_dt',
            'selected_marketing_account_id_list',
            'selected_marketing_team_id_list'
        ]
    ) + [task_processing] + statistics_field + ['order_statistics']
    search_fields = ['id']
